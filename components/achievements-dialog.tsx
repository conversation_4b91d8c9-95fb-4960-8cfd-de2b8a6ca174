"use client";

import * as React from "react";
import { format } from "date-fns";

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  EyeIcon,
  LoaderIcon,
  MoreHorizontalIcon,
  PencilEditIcon,
  TrashIcon,
} from "@/components/icons";
import { type Achievement, useAchievements } from "@/hooks/use-achievements";
import { ViewRawAchievementDialog } from "@/components/view-raw-achievement-dialog";
import { EditAchievementDialog } from "@/components/edit-achievement-dialog";

interface AchievementsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

function AchievementItem({
  achievement,
  onRefresh
}: {
  achievement: Achievement;
  onRefresh: () => void;
}) {
  const [isViewRawOpen, setIsViewRawOpen] = React.useState(false);
  const [isEditOpen, setIsEditOpen] = React.useState(false);

  const handleViewRaw = () => {
    setIsViewRawOpen(true);
  };

  const handleEdit = () => {
    setIsEditOpen(true);
  };

  const handleEditSuccess = () => {
    onRefresh();
  };

  const handleDelete = () => {
    console.log("Delete achievement:", achievement.id);
    // TODO: Implement delete achievement with confirmation
  };

  return (
    <div className="border-b border-border/50 pb-4 last:border-b-0 group">
      <div className="flex justify-between items-start gap-3">
        <div className="flex flex-col gap-2 flex-1 min-w-0">
          <div className="text-sm text-muted-foreground">
            {format(new Date(achievement.achievedAt), "MMM dd, yyyy")}
          </div>
          <div className="text-sm leading-relaxed">
            {achievement.achievement}
          </div>
        </div>

        <div className="shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="size-8 p-0 hover:bg-muted"
              >
                <MoreHorizontalIcon size={16} />
                <span className="sr-only">More options</span>
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={handleViewRaw}
              >
                <EyeIcon size={16} />
                <span>View Raw Achievement</span>
              </DropdownMenuItem>

              <DropdownMenuItem className="cursor-pointer" onClick={handleEdit}>
                <PencilEditIcon size={16} />
                <span>Edit Achievement</span>
              </DropdownMenuItem>

              <DropdownMenuItem
                className="cursor-pointer text-destructive focus:bg-destructive/15 focus:text-destructive dark:text-red-500"
                onClick={handleDelete}
              >
                <TrashIcon size={16} />
                <span>Delete Achievement</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <ViewRawAchievementDialog
        open={isViewRawOpen}
        onOpenChange={setIsViewRawOpen}
        achievement={achievement}
      />

      <EditAchievementDialog
        open={isEditOpen}
        onOpenChange={setIsEditOpen}
        achievement={achievement}
        onSuccess={handleEditSuccess}
      />
    </div>
  );
}

function EmptyState() {
  return (
    <div className="flex flex-col items-center justify-center py-8 text-center">
      <div className="text-muted-foreground text-sm">No achievements yet</div>
      <div className="text-muted-foreground text-xs mt-1">
        Start a conversation to track your accomplishments
      </div>
    </div>
  );
}

function LoadingState() {
  return (
    <div className="flex items-center justify-center py-8">
      <LoaderIcon size={20} />
      <span className="ml-2 text-sm text-muted-foreground">
        Loading achievements...
      </span>
    </div>
  );
}

function ErrorState() {
  return (
    <div className="flex flex-col items-center justify-center py-8 text-center">
      <div className="text-destructive text-sm">
        Failed to load achievements
      </div>
      <div className="text-muted-foreground text-xs mt-1">
        Please try again later
      </div>
    </div>
  );
}

export function AchievementsDialog({
  open,
  onOpenChange,
}: AchievementsDialogProps) {
  const { achievements, isLoading, error, mutate } = useAchievements();

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        side="center"
        className="max-h-[80vh] overflow-y-auto max-w-2xl"
      >
        <SheetHeader className="text-center">
          <SheetTitle>Your Achievements</SheetTitle>
          <SheetDescription>
            A record of your accomplishments and milestones
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6">
          {isLoading ? (
            <LoadingState />
          ) : error ? (
            <ErrorState />
          ) : achievements.length === 0 ? (
            <EmptyState />
          ) : (
            <div className="space-y-4">
              {achievements.map((achievement) => (
                <AchievementItem
                  key={achievement.id}
                  achievement={achievement}
                  onRefresh={mutate}
                />
              ))}
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
