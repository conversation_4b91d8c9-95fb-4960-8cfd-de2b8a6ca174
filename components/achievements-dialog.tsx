'use client';

import * as React from 'react';
import { format } from 'date-fns';

import {
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>eader,
  SheetTitle,
  SheetDescription,
} from '@/components/ui/sheet';
import { LoaderIcon } from '@/components/icons';
import { useAchievements, type Achievement } from '@/hooks/use-achievements';

interface AchievementsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

function AchievementItem({ achievement }: { achievement: Achievement }) {
  return (
    <div className="border-b border-border/50 pb-4 last:border-b-0">
      <div className="flex flex-col gap-2">
        <div className="text-sm text-muted-foreground">
          {format(new Date(achievement.achievedAt), 'MMM dd, yyyy')}
        </div>
        <div className="text-sm leading-relaxed">
          {achievement.achievement}
        </div>
      </div>
    </div>
  );
}

function EmptyState() {
  return (
    <div className="flex flex-col items-center justify-center py-8 text-center">
      <div className="text-muted-foreground text-sm">
        No achievements yet
      </div>
      <div className="text-muted-foreground text-xs mt-1">
        Start a conversation to track your accomplishments
      </div>
    </div>
  );
}

function LoadingState() {
  return (
    <div className="flex items-center justify-center py-8">
      <LoaderIcon size={20} />
      <span className="ml-2 text-sm text-muted-foreground">
        Loading achievements...
      </span>
    </div>
  );
}

function ErrorState() {
  return (
    <div className="flex flex-col items-center justify-center py-8 text-center">
      <div className="text-destructive text-sm">
        Failed to load achievements
      </div>
      <div className="text-muted-foreground text-xs mt-1">
        Please try again later
      </div>
    </div>
  );
}

export function AchievementsDialog({
  open,
  onOpenChange,
}: AchievementsDialogProps) {
  const { achievements, isLoading, error } = useAchievements();

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        side="center"
        className="max-h-[80vh] overflow-y-auto max-w-2xl"
      >
        <SheetHeader className="text-center">
          <SheetTitle>Your Achievements</SheetTitle>
          <SheetDescription>
            A record of your accomplishments and milestones
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6">
          {isLoading ? (
            <LoadingState />
          ) : error ? (
            <ErrorState />
          ) : achievements.length === 0 ? (
            <EmptyState />
          ) : (
            <div className="space-y-4">
              {achievements.map((achievement) => (
                <AchievementItem
                  key={achievement.id}
                  achievement={achievement}
                />
              ))}
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
