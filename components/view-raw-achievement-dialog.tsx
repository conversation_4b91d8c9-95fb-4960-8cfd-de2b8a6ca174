'use client';

import * as React from 'react';
import { format } from 'date-fns';

import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTitle,
  SheetDescription,
} from '@/components/ui/sheet';
import { type Achievement } from '@/hooks/use-achievements';

interface ViewRawAchievementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  achievement: Achievement | null;
}

export function ViewRawAchievementDialog({
  open,
  onOpenChange,
  achievement,
}: ViewRawAchievementDialogProps) {
  if (!achievement) return null;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        side="center"
        className="max-h-[80vh] overflow-y-auto max-w-3xl"
      >
        <SheetHeader className="text-center">
          <SheetTitle>Achievement Details</SheetTitle>
          <SheetDescription>
            Compare the processed and raw achievement text
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <h3 className="text-sm font-medium text-foreground">
                Processed Achievement
              </h3>
              <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                Enhanced
              </div>
            </div>
            <div className="p-4 bg-muted/50 rounded-lg border">
              <p className="text-sm leading-relaxed whitespace-pre-wrap">
                {achievement.achievement}
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <h3 className="text-sm font-medium text-foreground">
                Raw Achievement
              </h3>
              <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                Original
              </div>
            </div>
            <div className="p-4 bg-background rounded-lg border">
              <p className="text-sm leading-relaxed whitespace-pre-wrap">
                {achievement.achievementUnprocessed}
              </p>
            </div>
          </div>

          <div className="pt-2 border-t">
            <div className="text-xs text-muted-foreground">
              <span className="font-medium">Achievement Date:</span>{' '}
              {format(new Date(achievement.achievedAt), 'MMMM dd, yyyy')}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
