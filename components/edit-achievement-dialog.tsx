'use client';

import * as React from 'react';
import { format } from 'date-fns';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTitle,
  SheetDescription,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { LoaderIcon } from '@/components/icons';
import { type Achievement } from '@/hooks/use-achievements';
import { toast } from '@/components/toast';

interface EditAchievementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  achievement: Achievement | null;
  onSuccess: () => void;
}

interface FormData {
  achievement: string;
  achievedAt: string;
}

export function EditAchievementDialog({
  open,
  onOpenChange,
  achievement,
  onSuccess,
}: EditAchievementDialogProps) {
  const [formData, setFormData] = React.useState<FormData>({
    achievement: '',
    achievedAt: '',
  });
  const [isLoading, setIsLoading] = React.useState(false);
  const [errors, setErrors] = React.useState<Partial<FormData>>({});

  // Update form data when achievement changes
  React.useEffect(() => {
    if (achievement) {
      setFormData({
        achievement: achievement.achievement,
        achievedAt: format(new Date(achievement.achievedAt), 'yyyy-MM-dd'),
      });
      setErrors({});
    }
  }, [achievement]);

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    if (!formData.achievement.trim()) {
      newErrors.achievement = 'Achievement text is required';
    }

    if (!formData.achievedAt) {
      newErrors.achievedAt = 'Achievement date is required';
    } else {
      const date = new Date(formData.achievedAt);
      if (isNaN(date.getTime())) {
        newErrors.achievedAt = 'Please enter a valid date';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!achievement || !validateForm()) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/achievements/${achievement.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          achievement: formData.achievement.trim(),
          achievedAt: new Date(formData.achievedAt).toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update achievement');
      }

      toast({
        type: 'success',
        description: 'Achievement updated successfully!',
      });

      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating achievement:', error);
      toast({
        type: 'error',
        description: 'Failed to update achievement. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    // Reset form data to original values
    if (achievement) {
      setFormData({
        achievement: achievement.achievement,
        achievedAt: format(new Date(achievement.achievedAt), 'yyyy-MM-dd'),
      });
      setErrors({});
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  if (!achievement) return null;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        side="center"
        className="max-h-[80vh] overflow-y-auto max-w-2xl"
      >
        <SheetHeader className="text-center">
          <SheetTitle>Edit Achievement</SheetTitle>
          <SheetDescription>
            Modify the achievement text and date
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          <div className="space-y-2">
            <Label htmlFor="achievement-text">Achievement Text</Label>
            <Textarea
              id="achievement-text"
              placeholder="Enter achievement description..."
              value={formData.achievement}
              onChange={(e) => handleInputChange('achievement', e.target.value)}
              className={errors.achievement ? 'border-destructive' : ''}
              rows={4}
            />
            {errors.achievement && (
              <p className="text-sm text-destructive">{errors.achievement}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="achievement-date">Achievement Date</Label>
            <Input
              id="achievement-date"
              type="date"
              value={formData.achievedAt}
              onChange={(e) => handleInputChange('achievedAt', e.target.value)}
              className={errors.achievedAt ? 'border-destructive' : ''}
            />
            {errors.achievedAt && (
              <p className="text-sm text-destructive">{errors.achievedAt}</p>
            )}
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              onClick={handleSave}
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading && (
                <div className="mr-2">
                  <LoaderIcon size={16} />
                </div>
              )}
              Save Changes
            </Button>
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={isLoading}
              className="flex-1"
            >
              Cancel
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
