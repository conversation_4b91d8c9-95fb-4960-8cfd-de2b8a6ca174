'use client';

import { useCallback, useState } from 'react';
import { useSession } from 'next-auth/react';
import useSWR from 'swr';
import type { UserSettings } from '@/lib/db/schema';
import { fetcher, fetchWithErrorHandlers } from '@/lib/utils';

type PartialUserSettings = Partial<
  Pick<UserSettings, 'firstName' | 'jobRole' | 'jobDescription' | 'company'>
>;

interface UseUserSettingsReturn {
  isLoadingSettings: boolean;
  userSettings?: UserSettings;
  updateSettings: (partialUserSettings: PartialUserSettings) => Promise<void>;
  isUpdatingSettings: boolean;
  error?: string;
}

export function useUserSettings(): UseUserSettingsReturn {
  const { data: session } = useSession();
  const [isUpdatingSettings, setIsUpdatingSettings] = useState(false);

  // Only fetch user settings if user is authenticated
  const {
    data: userSettings,
    error,
    mutate,
    isLoading,
  } = useSWR<UserSettings | null>(
    session?.user?.id ? '/api/user-settings' : null,
    fetcher,
    {
      // Don't revalidate on focus for user settings as they don't change frequently
      revalidateOnFocus: false,
      // Keep data fresh for 5 minutes
      dedupingInterval: 5 * 60 * 1000,
    },
  );

  const updateSettings = useCallback(
    async (partialUserSettings: PartialUserSettings) => {
      if (!session?.user?.id) {
        throw new Error('User not authenticated');
      }

      setIsUpdatingSettings(true);

      try {
        const response = await fetchWithErrorHandlers('/api/user-settings', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(partialUserSettings),
        });

        const updatedSettings = await response.json();

        // Optimistically update the cache with the new settings
        await mutate(updatedSettings, false);

        return updatedSettings;
      } catch (error) {
        // Re-throw the error so the caller can handle it
        throw error;
      } finally {
        setIsUpdatingSettings(false);
      }
    },
    [session?.user?.id, mutate],
  );

  return {
    isLoadingSettings: isLoading,
    userSettings: userSettings || undefined,
    updateSettings,
    isUpdatingSettings,
    error: error?.message,
  };
}
