'use client';

import useSWR from 'swr';
import { fetcher } from '@/lib/utils';

export interface Achievement {
  id: number;
  achievement: string;
  achievementUnprocessed: string;
  createdAt: Date;
  achievedAt: Date;
}

export interface AchievementsResponse {
  achievements: Achievement[];
  count: number;
}

export function useAchievements() {
  const { data, error, isLoading, mutate } = useSWR<AchievementsResponse>(
    '/api/achievements',
    fetcher,
  );

  return {
    achievements: data?.achievements || [],
    count: data?.count || 0,
    isLoading,
    error,
    mutate,
  };
}
