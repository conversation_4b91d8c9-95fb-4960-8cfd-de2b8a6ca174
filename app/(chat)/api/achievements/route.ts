import { auth } from '@/app/(auth)/auth';
import { getAchievementsByUserId } from '@/lib/db/queries';
import { ChatSDKError } from '@/lib/errors';

export async function GET() {
  const session = await auth();

  if (!session?.user) {
    return new ChatSDKError('unauthorized:api').toResponse();
  }

  try {
    const achievements = await getAchievementsByUserId({
      userId: session.user.id,
    });

    return Response.json({
      achievements: achievements.map((achievement) => ({
        id: achievement.id,
        achievement: achievement.achievement,
        achievementUnprocessed: achievement.achievementUnprocessed,
        createdAt: achievement.createdAt,
        achievedAt: achievement.achievedAt,
      })),
      count: achievements.length,
    });
  } catch (error) {
    console.error('Failed to fetch achievements:', error);
    return new ChatSDKError(
      'bad_request:database',
      'Failed to fetch achievements',
    ).toResponse();
  }
}
