import { tool } from "ai";
import { z } from "zod";
import type { Session } from "next-auth";
import { createAchievement } from "@/lib/db/queries";

interface CreateAchievementProps {
  session: Session;
}

export const createAchievementTool = ({ session }: CreateAchievementProps) =>
  tool({
    description:
      "Create a new achievement record for the current authenticated user. Accepts both processed achievement text, raw unprocessed text, and an optional date when the achievement occurred.",
    inputSchema: z.object({
      achievement: z
        .string()
        .min(1)
        .max(2000)
        .describe("The processed achievement text"),
      achievementUnprocessed: z
        .string()
        .min(1)
        .max(2000)
        .describe("The raw, unprocessed achievement data"),
      achievedAt: z
        .string()
        .datetime()
        .optional()
        .describe(
          "ISO date string of when the achievement occurred (optional)",
        ),
    }),
    execute: async ({ achievement, achievementUnprocessed, achievedAt }) => {
      console.log(
        "Creating achievement",
        achievement,
        achievementUnprocessed,
        achievedAt,
      );

      try {
        const achievedAtDate = achievedAt ? new Date(achievedAt) : new Date();

        await createAchievement({
          achievement,
          achievementUnprocessed,
          userId: session.user.id,
          achievedAt: achievedAtDate,
        });

        return {
          success: true,
          message: "Achievement created successfully",
          achievement: {
            achievement,
            achievementUnprocessed,
            userId: session.user.id,
            achievedAt: achievedAtDate,
          },
        };
      } catch (error) {
        console.error(error);
        return {
          success: false,
          error: "Failed to create achievement",
        };
      }
    },
  });
