import { tool } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import { getAchievementById, updateAchievement } from '@/lib/db/queries';

interface UpdateAchievementProps {
  session: Session;
}

export const updateAchievementTool = ({ session }: UpdateAchievementProps) =>
  tool({
    description: 'Update an existing achievement record. Only allows updating achievements that belong to the current authenticated user. You can update the processed text, unprocessed text, achievement date, or any combination.',
    inputSchema: z.object({
      id: z.number().describe('The unique ID of the achievement to update'),
      achievement: z.string().min(1).max(2000).optional().describe('The updated processed achievement text'),
      achievementUnprocessed: z.string().min(1).max(2000).optional().describe('The updated raw, unprocessed achievement data'),
      achievedAt: z.string().datetime().optional().describe('ISO date string of when the achievement occurred (optional)'),
    }),
    execute: async ({ id, achievement, achievementUnprocessed, achievedAt }) => {
      try {
        // First, verify the achievement exists and belongs to the user
        const existingAchievement = await getAchievementById({ id });

        if (!existingAchievement) {
          return {
            success: false,
            error: 'Achievement not found',
          };
        }

        // Verify the achievement belongs to the current user
        if (existingAchievement.userId !== session.user.id) {
          return {
            success: false,
            error: 'You do not have permission to update this achievement',
          };
        }

        // Check if at least one field is provided for update
        if (!achievement && !achievementUnprocessed && achievedAt === undefined) {
          return {
            success: false,
            error: 'At least one field (achievement, achievementUnprocessed, or achievedAt) must be provided for update',
          };
        }

        // Convert achievedAt string to Date if provided
        const achievedAtDate = achievedAt ? new Date(achievedAt) : undefined;

        // Update the achievement
        const [updatedAchievement] = await updateAchievement({
          id,
          achievement,
          achievementUnprocessed,
          achievedAt: achievedAtDate,
        });

        return {
          success: true,
          message: 'Achievement updated successfully',
          achievement: {
            id: updatedAchievement.id,
            achievement: updatedAchievement.achievement,
            achievementUnprocessed: updatedAchievement.achievementUnprocessed,
            createdAt: updatedAchievement.createdAt,
            achievedAt: updatedAchievement.achievedAt,
            userId: updatedAchievement.userId,
          },
        };
      } catch (error) {
        return {
          success: false,
          error: 'Failed to update achievement',
        };
      }
    },
  });
